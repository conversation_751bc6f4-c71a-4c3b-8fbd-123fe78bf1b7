spring:
  neo4j:
    uri: bolt://localhost:7687
    authentication:
      username: neo4j
      password: 12345678
    database: neo4j
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *******************************************************************
    username: postgres
    password: 123456
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 15
      idle-timeout: 600000
      max-lifetime: 1800000
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启SQL日志
