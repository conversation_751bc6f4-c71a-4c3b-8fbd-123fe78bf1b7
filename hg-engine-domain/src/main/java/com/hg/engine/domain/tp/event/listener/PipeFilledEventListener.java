package com.hg.engine.domain.tp.event.listener;

import cn.hutool.core.collection.CollUtil;
import com.hg.engine.domain.tp.event.PipeFilledEvent;
import com.hg.engine.domain.tp.event.PipeStoppedEvent;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.domain.tp.repository.NodeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 水管注水事件监听器
 */
@Component
@RequiredArgsConstructor
public class PipeFilledEventListener extends NodeEventListener<PipeFilledEvent> {

    private final ApplicationEventPublisher eventPublisher;

    private final NodeRepository nodeRepository;

    @Override
    protected void handleEvent(PipeFilledEvent event) {
        // 获取当前水管节点
        Pipe node = (Pipe) nodeRepository.getById(event.getId());
        node.setFlow(true);
        nodeRepository.updateNode(node);
        // 获取下游节点列表
        List<Node> nextNodeList = nodeRepository.getNextNodeList(node.getId());
        // 分发下游事件
        for (Node nextNode : nextNodeList) {
            if (nextNode instanceof Pipe) {
                // 如果是水管，创建并发布 PipeFilledEvent
                PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(nextNode.getId());
                eventPublisher.publishEvent(pipeFilledEvent);
            } else if (nextNode instanceof Valve) {
                // 获取阀门的当前状态
                Valve valve = (Valve) nextNode;
                Boolean isValveOpen = valve.getOpen();

                // 如果阀门原本是关闭状态，不发送任何事件
                if (!Boolean.TRUE.equals(isValveOpen)) {
                    continue;
                }

                // 获取阀门的 openModeList
                List<String> openModeList = valve.getOpenMode();

                // 获取阀门的下游节点
                List<Node> downstreamNodes = nodeRepository.getNextNodeList(valve.getId());

                // 根据 openModeList 决定是否注水
                for (Node downstreamNode : downstreamNodes) {
                    if (downstreamNode instanceof Pipe) {
                        Pipe pipe = (Pipe) downstreamNode;

                        // 如果 openModeList 为空，走正常阀门逻辑
                        if (CollUtil.isEmpty(openModeList)) {
                            PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(pipe.getId());
                            eventPublisher.publishEvent(pipeFilledEvent);
                        } else {
                            // 如果 openModeList 不为空表示这是一个三角阀 前端会将指定管道id放入，判断是否是该管道
                            if (openModeList.contains(pipe.getId())) {
                                PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(pipe.getId());
                                eventPublisher.publishEvent(pipeFilledEvent);
                            } else {
                                // 停止不在 openModeList 中的管道流动
                                PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(pipe.getId());
                                eventPublisher.publishEvent(pipeStoppedEvent);
                            }
                        }
                    }
                }
            }
        }
    }

}
