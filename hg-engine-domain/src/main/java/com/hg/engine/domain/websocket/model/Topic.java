package com.hg.engine.domain.websocket.model;

/**
 * WebSocket主题枚举
 * 统一表示消息类型和内容分类
 */
public enum Topic {

    // 客户端发送的命令
    /**
     * 订阅命令
     */
    SUBSCRIBE("subscribe", "订阅命令"),

    /**
     * 取消订阅命令
     */
    UNSUBSCRIBE("unsubscribe", "取消订阅命令"),

    /**
     * 心跳命令
     */
    HEARTBEAT("heartbeat", "心跳命令"),

    // 服务端推送的事件/数据
    /**
     * 节点事件
     */
    NODE_EVENTS("node.events", "节点事件"),


    // 系统响应
    /**
     * 心跳响应
     */
    HEARTBEAT_RESPONSE("heartbeat.response", "心跳响应"),

    /**
     * 错误消息
     */
    ERROR("error", "错误消息"),

    /**
     * 成功消息
     */
    SUCCESS("success", "成功消息"),

    /**
     * 系统通知
     */
    NOTIFICATION("notification", "系统通知");

    private final String value;
    private final String description;

    Topic(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串值获取Topic枚举
     */
    public static Topic fromValue(String value) {
        for (Topic topic : Topic.values()) {
            if (topic.getValue().equals(value)) {
                return topic;
            }
        }
        throw new IllegalArgumentException("Unknown topic value: " + value);
    }

    /**
     * 检查是否为客户端命令
     */
    public boolean isClientCommand() {
        return this == SUBSCRIBE || this == UNSUBSCRIBE || this == HEARTBEAT;
    }

    /**
     * 检查是否为服务端事件
     */
    public boolean isServerEvent() {
        return this == NODE_EVENTS;
    }

    /**
     * 检查是否为系统响应
     */
    public boolean isSystemResponse() {
        return this == HEARTBEAT_RESPONSE || this == ERROR || this == SUCCESS || this == NOTIFICATION;
    }

    @Override
    public String toString() {
        return value;
    }
}
