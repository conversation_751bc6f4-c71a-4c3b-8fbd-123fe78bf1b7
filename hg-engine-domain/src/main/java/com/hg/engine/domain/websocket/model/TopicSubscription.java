package com.hg.engine.domain.websocket.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 主题订阅领域值对象
 */
@Data
@EqualsAndHashCode(of = {"sessionId", "topic"})
public class TopicSubscription {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 订阅的主题
     */
    private String topic;
    
    /**
     * 订阅时间
     */
    private LocalDateTime subscribeTime;
    
    /**
     * 订阅状态
     */
    private SubscriptionStatus status;
    
    /**
     * 订阅过滤器（可选）
     */
    private String filter;
    
    public TopicSubscription() {
        this.subscribeTime = LocalDateTime.now();
        this.status = SubscriptionStatus.ACTIVE;
    }
    
    public TopicSubscription(String sessionId, String topic) {
        this();
        this.sessionId = sessionId;
        this.topic = topic;
    }
    
    /**
     * 激活订阅
     */
    public void activate() {
        this.status = SubscriptionStatus.ACTIVE;
    }
    
    /**
     * 暂停订阅
     */
    public void pause() {
        this.status = SubscriptionStatus.PAUSED;
    }
    
    /**
     * 取消订阅
     */
    public void cancel() {
        this.status = SubscriptionStatus.CANCELLED;
    }
    
    /**
     * 检查订阅是否活跃
     */
    public boolean isActive() {
        return this.status == SubscriptionStatus.ACTIVE;
    }
    
    /**
     * 检查消息是否匹配过滤器
     */
    public boolean matchesFilter(Object message) {
        // 简单实现，后续可以扩展为更复杂的过滤逻辑
        if (filter == null || filter.trim().isEmpty()) {
            return true;
        }
        
        // 这里可以实现更复杂的过滤逻辑
        // 例如：JSON路径过滤、正则表达式匹配等
        return true;
    }
}
