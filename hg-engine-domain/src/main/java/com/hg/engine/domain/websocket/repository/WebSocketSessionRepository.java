package com.hg.engine.domain.websocket.repository;

import com.hg.engine.domain.websocket.model.WebSocketSession;

import java.util.List;
import java.util.Optional;

/**
 * WebSocket会话仓储接口
 */
public interface WebSocketSessionRepository {
    
    /**
     * 保存会话
     */
    void save(WebSocketSession session);
    
    /**
     * 根据会话ID查找会话
     */
    Optional<WebSocketSession> findById(String sessionId);
    
    /**
     * 根据用户ID查找会话
     */
    List<WebSocketSession> findByUserId(String userId);
    
    /**
     * 查找订阅了指定主题的所有会话
     */
    List<WebSocketSession> findBySubscribedTopic(String topic);
    
    /**
     * 查找所有活跃会话
     */
    List<WebSocketSession> findAllActive();
    
    /**
     * 删除会话
     */
    void delete(String sessionId);
    
    /**
     * 删除超时会话
     */
    void deleteTimeoutSessions(long timeoutSeconds);
    
    /**
     * 获取会话总数
     */
    long count();
    
    /**
     * 获取活跃会话数
     */
    long countActive();
}
