package com.hg.engine.domain.websocket.service;

import com.hg.engine.domain.websocket.model.Topic;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.model.WebSocketSession;
import com.hg.engine.domain.websocket.repository.WebSocketSessionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * WebSocket领域服务
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WebSocketDomainService {

    private final WebSocketSessionRepository sessionRepository;
    private final WebSocketMessageSender messageSender;
    private final TopicSubscriptionService subscriptionService;

    /**
     * 处理新连接
     */
    public void handleConnect(WebSocketSession session) {
        log.info("处理新连接: sessionId={}, userId={}", session.getSessionId(), session.getUserId());

        // 保存会话
        sessionRepository.save(session);

        // 发送连接成功消息
        WebSocketMessage welcomeMessage = WebSocketMessage.success("连接成功");
        messageSender.sendToSession(session.getSessionId(), welcomeMessage);

        // 自动订阅默认主题
        subscribeToDefaultTopics(session.getSessionId());
    }

    /**
     * 处理断开连接
     */
    public void handleDisconnect(String sessionId) {
        log.info("处理断开连接: sessionId={}", sessionId);

        Optional<WebSocketSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            WebSocketSession session = sessionOpt.get();

            // 取消所有订阅
            subscriptionService.unsubscribeAll(sessionId);

            // 关闭会话
            session.close();

            // 删除会话
            sessionRepository.delete(sessionId);
        }
    }

    /**
     * 处理消息
     */
    public void handleMessage(String sessionId, WebSocketMessage message) {
        log.debug("处理消息: sessionId={}, messageType={}, topic={}",
                sessionId, message.getTopic());

        Optional<WebSocketSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isEmpty()) {
            log.warn("会话不存在: sessionId={}", sessionId);
            return;
        }

        WebSocketSession session = sessionOpt.get();
        session.updateLastActiveTime();
        sessionRepository.save(session);

        Topic topic = message.getTopic();

        switch (topic) {
            case SUBSCRIBE:
                handleSubscribe(sessionId, (String) message.getPayload());
                break;
            case UNSUBSCRIBE:
                handleUnsubscribe(sessionId, (String) message.getPayload());
                break;
            case HEARTBEAT:
                handleHeartbeat(sessionId);
                break;
            default:
                log.warn("客户端不应发送此类型消息: {}", topic);
        }
    }

    /**
     * 处理订阅
     */
    private void handleSubscribe(String sessionId, String targetTopicValue) {
        try {
            // 验证目标主题是否有效
            Topic.fromValue(targetTopicValue);

            subscriptionService.subscribe(sessionId, targetTopicValue);

            WebSocketMessage response = WebSocketMessage.success("订阅成功: " + targetTopicValue);
            messageSender.sendToSession(sessionId, response);

            log.info("会话订阅主题: sessionId={}, topic={}", sessionId, targetTopicValue);
        } catch (IllegalArgumentException e) {
            WebSocketMessage response = WebSocketMessage.error("无效的主题: " + targetTopicValue);
            messageSender.sendToSession(sessionId, response);
        }
    }

    /**
     * 处理取消订阅
     */
    private void handleUnsubscribe(String sessionId, String targetTopicValue) {
        subscriptionService.unsubscribe(sessionId, targetTopicValue);

        WebSocketMessage response = WebSocketMessage.success("取消订阅成功: " + targetTopicValue);
        messageSender.sendToSession(sessionId, response);

        log.info("会话取消订阅主题: sessionId={}, topic={}", sessionId, targetTopicValue);
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(String sessionId) {
        WebSocketMessage response = WebSocketMessage.heartbeatResponse();
        messageSender.sendToSession(sessionId, response);
    }

    /**
     * 处理发布消息
     */
    private void handlePublish(WebSocketMessage message) {
        messageSender.sendToTopic(message.getTopic(), message);
        log.debug("发布消息到主题: topic={}", message.getTopic());
    }

    /**
     * 自动订阅默认主题
     */
    public void subscribeToDefaultTopics(String sessionId) {
        List<Topic> defaultTopics = List.of(Topic.HEARTBEAT_RESPONSE, Topic.NODE_EVENTS);
        for (Topic topic : defaultTopics) {
            subscriptionService.subscribe(sessionId, topic.getValue());
            log.debug("自动订阅默认主题: sessionId={}, topic={}", sessionId, topic.getValue());
        }
    }

    /**
     * 清理超时会话
     */
    public void cleanupTimeoutSessions(long timeoutSeconds) {
        log.debug("清理超时会话: timeoutSeconds={}", timeoutSeconds);
        sessionRepository.deleteTimeoutSessions(timeoutSeconds);
    }

    /**
     * 获取会话统计信息
     */
    public SessionStats getSessionStats() {
        long totalSessions = sessionRepository.count();
        long activeSessions = sessionRepository.countActive();

        return new SessionStats(totalSessions, activeSessions);
    }

    /**
     * 会话统计信息
     */
    public static class SessionStats {
        private final long totalSessions;
        private final long activeSessions;

        public SessionStats(long totalSessions, long activeSessions) {
            this.totalSessions = totalSessions;
            this.activeSessions = activeSessions;
        }

        public long getTotalSessions() {
            return totalSessions;
        }

        public long getActiveSessions() {
            return activeSessions;
        }
    }
}
