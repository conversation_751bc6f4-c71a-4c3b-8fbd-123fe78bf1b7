package com.hg.engine.domain.tp.command;

import com.alibaba.fastjson2.JSON;
import com.hg.engine.shared.exception.ServeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 参数解析器
 * 负责将原始参数解析为具体的参数对象
 */
@Slf4j
@Component
public class ParameterParser {
    
    /**
     * 解析参数
     * 
     * @param rawParameters 原始参数（通常是Map或JSON字符串）
     * @param parameterType 目标参数类型
     * @return 解析后的参数对象
     */
    @SuppressWarnings("unchecked")
    public <T extends CommandParameter> T parseParameter(Object rawParameters, Class<T> parameterType) {
        if (rawParameters == null) {
            // 如果没有参数，尝试创建默认实例
            return createDefaultInstance(parameterType);
        }
        
        try {
            T parameter;
            
            if (rawParameters instanceof String) {
                // JSON字符串解析
                parameter = JSON.parseObject((String) rawParameters, parameterType);
            } else {
                // 对象转换
                String jsonStr = JSON.toJSONString(rawParameters);
                parameter = JSON.parseObject(jsonStr, parameterType);
            }
            // 验证参数
            if (parameter != null) {
                parameter.validate();
            }
            return parameter;
        } catch (Exception e) {
            log.error("参数解析失败: parameterType={}, rawParameters={}", 
                     parameterType.getSimpleName(), rawParameters, e);
            throw new ServeException("参数解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建默认参数实例
     */
    private <T extends CommandParameter> T createDefaultInstance(Class<T> parameterType) {
        try {
            return parameterType.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.warn("无法创建默认参数实例: {}", parameterType.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 检查参数类型是否有效
     */
    public boolean isValidParameterType(Class<?> parameterType) {
        return parameterType != null && 
               CommandParameter.class.isAssignableFrom(parameterType) &&
               parameterType != CommandParameter.class;
    }
}
