package com.hg.engine.domain.tp.annotation;

import com.hg.engine.domain.tp.command.CommandParameter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 指令处理器注解
 * 标记在领域服务的方法上，表示该方法可以处理指定的指令
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CommandHandler {
    
    /**
     * 指令
     */
    String command();

    /**
     * 示例
     */
    String example() default "";
    
    /**
     * 指令参数类型
     * 用于自动解析和验证参数
     */
    Class<? extends CommandParameter> parameterType() default CommandParameter.class;
    
    /**
     * 指令描述
     */
    String description() default "";
}
