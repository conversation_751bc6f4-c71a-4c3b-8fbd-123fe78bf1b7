package com.hg.engine.domain.websocket.service;

import com.hg.engine.domain.websocket.model.Topic;
import com.hg.engine.domain.websocket.model.WebSocketMessage;

import java.util.List;

/**
 * WebSocket消息发送服务接口
 */
public interface WebSocketMessageSender {
    
    /**
     * 向指定会话发送消息
     */
    void sendToSession(String sessionId, WebSocketMessage message);
    
    /**
     * 向指定用户发送消息
     */
    void sendToUser(String userId, WebSocketMessage message);
    
    /**
     * 向订阅了指定主题的所有会话发送消息
     */
    void sendToTopic(Topic topic, WebSocketMessage message);
    
    /**
     * 向多个会话发送消息
     */
    void sendToSessions(List<String> sessionIds, WebSocketMessage message);
    
    /**
     * 广播消息给所有活跃会话
     */
    void broadcast(WebSocketMessage message);
    
    /**
     * 检查会话是否在线
     */
    boolean isSessionOnline(String sessionId);
}
