package com.hg.engine.domain.websocket.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * WebSocket消息领域值对象
 * 使用Topic统一表示消息类型和内容分类
 */
@Data
public class WebSocketMessage {

    /**
     * 消息主题（统一表示消息类型和内容分类）
     */
    private Topic topic;
    
    /**
     * 消息负载
     */
    private Object payload;
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 发送者会话ID
     */
    private String sessionId;
    
    /**
     * 消息时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 消息头部信息
     */
    private Map<String, Object> headers;
    
    public WebSocketMessage() {
        this.timestamp = LocalDateTime.now();
    }
    
    public WebSocketMessage(Topic topic, Object payload) {
        this();
        this.topic = topic;
        this.payload = payload;
    }
    
    /**
     * 创建订阅消息
     */
    public static WebSocketMessage subscribe(String targetTopic) {
        return new WebSocketMessage(Topic.SUBSCRIBE, targetTopic);
    }

    /**
     * 创建取消订阅消息
     */
    public static WebSocketMessage unsubscribe(String targetTopic) {
        return new WebSocketMessage(Topic.UNSUBSCRIBE, targetTopic);
    }

    /**
     * 创建心跳消息
     */
    public static WebSocketMessage heartbeat() {
        return new WebSocketMessage(Topic.HEARTBEAT, "ping");
    }

    /**
     * 创建心跳响应消息
     */
    public static WebSocketMessage heartbeatResponse() {
        return new WebSocketMessage(Topic.HEARTBEAT_RESPONSE, "pong");
    }

    /**
     * 创建错误消息
     */
    public static WebSocketMessage error(String message) {
        return new WebSocketMessage(Topic.ERROR, message);
    }

    /**
     * 创建成功响应消息
     */
    public static WebSocketMessage success(String message) {
        return new WebSocketMessage(Topic.SUCCESS, message);
    }

    /**
     * 创建Node事件消息
     */
    public static WebSocketMessage nodeEvent(String nodeId, Object nodeData) {
        WebSocketMessage message = new WebSocketMessage(Topic.NODE_EVENTS, nodeData);
        if (message.headers == null) {
            message.headers = new java.util.HashMap<>();
        }
        message.headers.put("nodeId", nodeId);
        return message;
    }

    /**
     * 创建通用消息
     */
    public static WebSocketMessage of(Topic topic, Object payload) {
        return new WebSocketMessage(topic, payload);
    }
}
