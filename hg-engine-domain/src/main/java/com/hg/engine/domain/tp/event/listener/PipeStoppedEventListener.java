package com.hg.engine.domain.tp.event.listener;

import com.hg.engine.domain.tp.event.PipeStoppedEvent;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.domain.tp.repository.NodeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 水管停止流动事件监听器
 */
@Component
@RequiredArgsConstructor
public class PipeStoppedEventListener extends NodeEventListener<PipeStoppedEvent> {

    private final ApplicationEventPublisher eventPublisher;

    private final NodeRepository nodeRepository;


    @Override
    protected void handleEvent(PipeStoppedEvent event) {
        // 获取当前水管节点
        Pipe node = (Pipe) nodeRepository.getById(event.getId());
        // 设置水管为停止流动状态
        node.setFlow(false);
        nodeRepository.updateNode(node);

        // 获取下游节点列表
        List<Node> nextNodeList = nodeRepository.getNextNodeList(node.getId());

        // 分发下游事件
        for (Node nextNode : nextNodeList) {
            if (nextNode instanceof Pipe) {
                // 如果是水管，创建并发布 PipeStoppedEvent
                // 判断是否有水
                List<Node> lastNodeList = nodeRepository.getLastNodeList(nextNode.getId());
                boolean isWater = false;
                for (Node last : lastNodeList) {
                    if (last instanceof Pipe) {
                        Pipe pipe = (Pipe) last;
                        if (Boolean.TRUE.equals(pipe.getFlow())) {
                            isWater = true;
                            break;
                        }
                    }
                }
                if (isWater) {
                    continue;
                }
                PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(nextNode.getId());
                eventPublisher.publishEvent(pipeStoppedEvent);
            } else if (nextNode instanceof Valve) {
                // 阀门状态保持不变，但需要继续传播到下游节点
                List<Node> downstreamNodes = nodeRepository.getNextNodeList(nextNode.getId());
                for (Node downstreamNode : downstreamNodes) {
                    if (downstreamNode instanceof Pipe) {
                        // 如果下游节点是水管，创建并发布 PipeStoppedEvent
                        PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(downstreamNode.getId());
                        eventPublisher.publishEvent(pipeStoppedEvent);
                    }
                }
            }
        }
    }

}
