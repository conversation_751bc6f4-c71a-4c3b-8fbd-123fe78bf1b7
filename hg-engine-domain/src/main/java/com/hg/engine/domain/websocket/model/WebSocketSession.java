package com.hg.engine.domain.websocket.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket会话领域实体
 */
@Data
@EqualsAndHashCode(of = "sessionId")
public class WebSocketSession {

    /**
     * 会话唯一标识
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 会话创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 会话状态
     */
    private SessionStatus status;

    /**
     * 订阅的主题集合
     */
    private Set<String> subscribedTopics;

    /**
     * 会话属性
     */
    private ConcurrentHashMap<String, Object> attributes;

    public WebSocketSession() {
        this.subscribedTopics = ConcurrentHashMap.newKeySet();
        this.attributes = new ConcurrentHashMap<>();
        this.createTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.status = SessionStatus.CONNECTED;
    }

    public WebSocketSession(String sessionId, String userId) {
        this();
        this.sessionId = sessionId;
        this.userId = userId;
    }

    /**
     * 订阅主题
     */
    public void subscribeTopic(String topic) {
        this.subscribedTopics.add(topic);
        updateLastActiveTime();
    }

    /**
     * 取消订阅主题
     */
    public void unsubscribeTopic(String topic) {
        this.subscribedTopics.remove(topic);
        updateLastActiveTime();
    }

    /**
     * 检查是否订阅了指定主题
     */
    public boolean isSubscribedTo(String topic) {
        return this.subscribedTopics.contains(topic);
    }

    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }

    /**
     * 设置会话属性
     */
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }

    /**
     * 获取会话属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) this.attributes.get(key);
    }

    /**
     * 关闭会话
     */
    public void close() {
        this.status = SessionStatus.DISCONNECTED;
        this.subscribedTopics.clear();
        this.attributes.clear();
    }

    /**
     * 检查会话是否活跃
     */
    public boolean isActive() {
        return this.status == SessionStatus.CONNECTED;
    }

    /**
     * 检查会话是否超时
     */
    public boolean isTimeout(long timeoutSeconds) {
        return LocalDateTime.now().isAfter(lastActiveTime.plusSeconds(timeoutSeconds));
    }
}
