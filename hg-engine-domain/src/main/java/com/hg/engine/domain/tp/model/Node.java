package com.hg.engine.domain.tp.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public abstract class Node {
    /**
     * 组件唯一标识符
     */
    String id;

    String type;


    String viewId;

    String name;

    String systemId;
    /**
     * 组件标签，用于显示
     */
    private String label;

    /**
     * 是否隐藏组件
     * true: 隐藏
     * false: 显示
     */
    private Boolean hide;

    /**
     * 是否锁定组件
     * true: 锁定，不可编辑
     * false: 解锁，可编辑
     */
    private Boolean lock;
    /**
     * 上游节点列表
     */
    @JSONField(serialize = false, deserialize = false)
    private List<Node> lastNode;

    /**
     * 下游节点列表，表示流体流向的下一个节点ID集合
     */
    @JSONField(serialize = false, deserialize = false)
    private List<Node> nextNode;

    /**
     * 组件属性集合，包含组件的详细配置信息
     * 如管道的绘制属性、阀门的特殊配置等
     */

    private String property;

}
