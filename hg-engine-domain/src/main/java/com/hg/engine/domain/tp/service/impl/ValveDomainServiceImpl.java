package com.hg.engine.domain.tp.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.hg.engine.domain.tp.annotation.CommandDomainService;
import com.hg.engine.domain.tp.annotation.CommandHandler;
import com.hg.engine.domain.tp.command.OpenValveParameter;
import com.hg.engine.domain.tp.event.PipeFilledEvent;
import com.hg.engine.domain.tp.event.PipeStoppedEvent;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.domain.tp.repository.NodeRepository;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.service.WebSocketDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;

import java.util.List;

@Slf4j
@CommandDomainService(nodeType = "hgValve", description = "阀门指令集")
@RequiredArgsConstructor
public class ValveDomainServiceImpl {
    private final NodeRepository nodeRepository;
    private final ApplicationEventPublisher eventPublishingService;
    private final WebSocketDomainService webSocketDomainService;

    /**
     * 打开阀门指令处理
     */
    @CommandHandler(command = "OPEN_VALVE", parameterType = OpenValveParameter.class,
            example = "{openMode:[]//打开下级水管的id，如果没有就}",
            description = "打开阀门")
    public Node openValve(String id, OpenValveParameter parameter) {
        Valve valve = (Valve) nodeRepository.getNodeWithRelations(id);
        valve.openValve(parameter);
        nodeRepository.updateNode(valve);
        //发送当前节点给前端
        webSocketDomainService.handleMessage(id, WebSocketMessage.nodeEvent(id, valve));
        //发布下级管道注水事件
        boolean isWater = valve.isWater();
        if (isWater) {
            List<String> openModeList = valve.getOpenMode();
            for (Node nextNode : valve.getNextNode()) {
                if (nextNode instanceof Pipe) {
                    Pipe pipe = (Pipe) nextNode;
                    // 如果 openModeList 为空，走正常阀门逻辑
                    if (CollUtil.isEmpty(openModeList)) {
                        PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(pipe.getId());
                        eventPublishingService.publishEvent(pipeFilledEvent);
                    } else {
                        // 如果 openModeList 包含该管道，注水
                        if (openModeList.contains(pipe.getId())) {
                            PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(pipe.getId());
                            eventPublishingService.publishEvent(pipeFilledEvent);
                        } else {
                            // 停止不在 openModeList 中的管道流动
                            PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(pipe.getId());
                            eventPublishingService.publishEvent(pipeStoppedEvent);
                        }
                    }
                }
            }
        }
        return valve;
    }

    /**
     * 打开阀门指令处理
     */
    @CommandHandler(command = "CLOSE_VALVE", description = "关闭阀门")
    public Node closeValve(String id) {
        // 获取当前阀门节点
        Valve node = (Valve) nodeRepository.getNodeWithRelations(id);
        node.closeValve();
        nodeRepository.updateNode(node);
        // 分发水管停水事件
        for (Node nextNode : node.getNextNode()) {
            if (nextNode instanceof Pipe) {
                // 如果是水管，创建并发布 PipeStoppedEvent
                PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(nextNode.getId());
                eventPublishingService.publishEvent(pipeStoppedEvent);
            }
        }
        return node;
    }

}
