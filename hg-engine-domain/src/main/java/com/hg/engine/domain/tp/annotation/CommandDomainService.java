package com.hg.engine.domain.tp.annotation;

import org.springframework.stereotype.Service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 领域服务注解
 * 标记在领域服务类上，表示该类包含指令处理方法
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Service
public @interface CommandDomainService {
    /**
     * 支持的节点类型（可选，为空表示支持所有类型）
     */
    String nodeType();

    /**
     * 服务名称
     */
    String value() default "";

    /**
     * 服务描述
     */
    String description() default "";
}
