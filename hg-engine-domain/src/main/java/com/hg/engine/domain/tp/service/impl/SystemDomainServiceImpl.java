package com.hg.engine.domain.tp.service.impl;

import com.hg.engine.domain.tp.model.System;
import com.hg.engine.domain.tp.repository.SystemRepository;
import com.hg.engine.domain.tp.service.SystemDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SystemDomainServiceImpl implements SystemDomainService {

    private final SystemRepository systemRepository;

}
