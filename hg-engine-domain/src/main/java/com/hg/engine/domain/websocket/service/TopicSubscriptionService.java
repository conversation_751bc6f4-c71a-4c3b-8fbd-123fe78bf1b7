package com.hg.engine.domain.websocket.service;

import com.hg.engine.domain.websocket.model.TopicSubscription;

import java.util.List;

/**
 * 主题订阅服务接口
 */
public interface TopicSubscriptionService {
    
    /**
     * 订阅主题
     */
    void subscribe(String sessionId, String topic);
    
    /**
     * 订阅主题（带过滤器）
     */
    void subscribe(String sessionId, String topic, String filter);
    
    /**
     * 取消订阅主题
     */
    void unsubscribe(String sessionId, String topic);
    
    /**
     * 取消会话的所有订阅
     */
    void unsubscribeAll(String sessionId);
    
    /**
     * 获取会话的所有订阅
     */
    List<TopicSubscription> getSubscriptions(String sessionId);
    
    /**
     * 获取订阅了指定主题的所有会话ID
     */
    List<String> getSubscribedSessions(String topic);
    
    /**
     * 检查会话是否订阅了指定主题
     */
    boolean isSubscribed(String sessionId, String topic);
    
    /**
     * 获取主题的订阅数量
     */
    long getSubscriptionCount(String topic);
    
    /**
     * 获取所有主题列表
     */
    List<String> getAllTopics();
}
