<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.hg.engine</groupId>
    <artifactId>hg-engine-server</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>hg-engine-server</name>
    <description>HG Engine Server - DDD Architecture</description>
    <modules>
        <module>hg-engine-api</module>
        <module>hg-engine-application</module>
        <module>hg-engine-domain</module>
        <module>hg-engine-infrastructure</module>
        <module>hg-engine-shared</module>
        <module>hg-engine-starter</module>
    </modules>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatisPlus.version>3.5.6</mybatisPlus.version>
        <hutool.version>5.8.38</hutool.version>
        <commons.lang3.version>3.13.0</commons.lang3.version>
        <commons.io.version>2.13.0</commons.io.version>
        <commons.collections4.version>4.4</commons.collections4.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <netty.version>4.1.100.Final</netty.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <fastjson.version>2.0.32</fastjson.version>
        <knife4j.version>4.4.0</knife4j.version>
        <jjwt.version>0.9.1</jjwt.version>
        <lombok.version>1.18.26</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.hg.engine</groupId>
                <artifactId>hg-engine-api</artifactId>
                <version>${project.version}</version>
            </dependency>


            <dependency>
                <groupId>com.hg.engine</groupId>
                <artifactId>hg-engine-domain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hg.engine</groupId>
                <artifactId>hg-engine-application</artifactId>
                <version>${project.version}</version>
            </dependency>


            <dependency>
                <groupId>com.hg.engine</groupId>
                <artifactId>hg-engine-shared</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hg.engine</groupId>
                <artifactId>hg-engine-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.26</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatisPlus.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- PostgreSQL 16.9 JDBC 驱动 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.7.7</version>
            </dependency>


            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <log.level>info</log.level>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <log.level>info</log.level>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <!-- Maven编译插件 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <compilerArg>
                            -Amapstruct.verbose=true
                        </compilerArg>
                        <compilerArg>
                            <!--   以spring注入的方式访问mapper-->
                            -Amapstruct.defaultComponentModel=spring
                        </compilerArg>
                        <compilerArg>
                            <!--   注入方式 默认是字段注入 设置为constructor是构造器注入-->
                            -Amapstruct.defaultInjectionStrategy=field
                        </compilerArg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
