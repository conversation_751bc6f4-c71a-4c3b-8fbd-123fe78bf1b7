package com.hg.engine.application.tp.convertor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hg.engine.application.tp.dto.SystemDTO;
import com.hg.engine.domain.tp.model.Node;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class SystemGraphConverter {


    private final NodeDTOConverter nodeDTOConverter;
    private final com.hg.engine.application.tp.convertor.SystemDTOConverter systemDTOConverter;


    public List<Node> getNodes(List<Node> all, List<String> nids) {
        List<Node> ns = new ArrayList<>();
        for (Node n : all) {
            for (String nid : nids) {
                if (nid.equals(n.getId())) {
                    ns.add(n);
                }
//                else {
//                    log.error("节点ID不匹配:{}", nid);
//                }
            }
        }
        return ns;
    }

    /**
     * 将 PipelineSystemDTO 中的 items 转换为 Node 列表，并建立节点间的关系
     *
     * @param pipelineSystem 管道系统DTO
     * @return Node 列表
     */
    public List<Node> convertToNodes(SystemDTO pipelineSystem) {
        if (pipelineSystem.getItems() == null || pipelineSystem.getItems().isEmpty()) {
            return new ArrayList<>();
        }

        // 第一步：先转换所有节点，建立ID到Node的映射
        List<Node> nodelist = new ArrayList<>();
        Map<String, Object> items = pipelineSystem.getItems();
        // 映射所有节点ID与nextNode 的映射 :       "nextNode": ["1"],
        Map<String, List<String>> nextNodeIdList = new HashMap<>();

        // 第一遍：创建所有节点
        for (Map.Entry<String, Object> entry : items.entrySet()) {
//          跳过底图
            if (entry.getKey().contains("svg_")) {
                continue;
            }
            Object itemJson = entry.getValue();
            Node node = nodeDTOConverter.json2Node(itemJson);
            if (node != null) {
                nodelist.add(node);
                Map<String, Object> itemMap = (Map<String, Object>) itemJson;
                // 处理下游节点关系
                Object nextNodesObj = itemMap.get("nextNode");
                if (nextNodesObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> nextNodeIds = (List<String>) nextNodesObj;
                    nextNodeIdList.put(node.getId(), nextNodeIds);
                }
            }
        }
        for (Node node1 : nodelist) {
            //根据 节点id 获取对应下游节点 id 列表
            List<String> nids = nextNodeIdList.get(node1.getId());
            // 将所有节点都绑定系统id
            node1.setSystemId(pipelineSystem.getId());
            if (CollUtil.isEmpty(nids)) {
                continue;
            }
//            根据节点id列表 获取对应节点
            List<Node> nexs = getNodes(nodelist, nids);
            for (Node node : nexs) {
                node.setSystemId(pipelineSystem.getId());
            }
            node1.setNextNode(nexs);
        }

        Map<String, Node> nodeMap = nodelist.stream().collect(Collectors.toMap(Node::getId, node -> node));
        Set<String> deleteNodeIds = new HashSet<>();
        // 第三步：构建节点之间的连接关系
        for (Node node : nodelist) {
            Node nodePO = nodeMap.get(node.getId());
            List<Node> targets = new ArrayList<>();
            // 添加空值检查
            List<Node> nextNodes = node.getNextNode();
            if (nextNodes != null) {
                for (Node nextNode : nextNodes) {
                    Node target = nodeMap.get(nextNode.getId());
                    if (target != null) {
                        targets.add(target);
                        deleteNodeIds.add(nextNode.getId());
                    }
                }
            }
            nodePO.setNextNode(targets);
        }//  保存节点数据

        List<Node> collect = nodelist.stream().filter(i -> !deleteNodeIds.contains(i.getId())).collect(Collectors.toList());

        return collect;
    }

    public void generateItems(SystemDTO dto, List<Node> nodes) {
        Map<String, Object> items = new HashMap<>();
        for (Node node : nodes) {
            String id = node.getId();
            String viewId = node.getViewId();
            node.setId(viewId);
            Map<String, Object> objMap = BeanUtil.beanToMap(node);
            objMap.put("nodeId", id);
            String property = node.getProperty();
            JSONObject jsonObject = JSON.parseObject(property);
            objMap.put("property", jsonObject);
//            获取第二层所有节点id
            List<Node> nextNode = (List<Node>) objMap.getOrDefault("nextNode", new ArrayList<>());
            List<String> nextNodeIdList = nextNode.stream().map(Node::getId).collect(Collectors.toList());
            objMap.put("nextNode", nextNodeIdList);
            items.put(viewId, objMap);
        }
        dto.setItems(items);
    }
}
