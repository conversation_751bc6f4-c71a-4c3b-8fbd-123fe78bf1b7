package com.hg.engine.application.websocket.convertor;

import com.hg.engine.application.websocket.dto.NodeWebSocketDTO;
import com.hg.engine.application.websocket.dto.PipeWebSocketDTO;
import com.hg.engine.application.websocket.dto.ValveWebSocketDTO;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Node到WebSocket DTO的转换器
 * 过滤掉label、hide、lock、lastNode、nextNode、property属性
 */
@Mapper(componentModel = "spring")
public interface NodeWebSocketConverter {
    
    /**
     * 通用Node转换方法
     */
    default NodeWebSocketDTO nodeToWebSocketDTO(Node node) {
        if (node instanceof Valve) {
            return valveToWebSocketDTO((Valve) node);
        } else if (node instanceof Pipe) {
            return pipeToWebSocketDTO((Pipe) node);
        }
        throw new IllegalArgumentException("Unsupported node type: " + node.getClass());
    }
    
    /**
     * Valve转换为ValveWebSocketDTO
     * 排除label、hide、lock、lastNode、nextNode、property属性
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "type", source = "type")
    @Mapping(target = "viewId", source = "viewId")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "systemId", source = "systemId")
    @Mapping(target = "valveType", source = "valveType")
    @Mapping(target = "open", source = "open")
    @Mapping(target = "openMode", source = "openMode")
    ValveWebSocketDTO valveToWebSocketDTO(Valve valve);
    
    /**
     * Pipe转换为PipeWebSocketDTO
     * 排除label、hide、lock、lastNode、nextNode、property属性
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "type", source = "type")
    @Mapping(target = "viewId", source = "viewId")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "systemId", source = "systemId")
    @Mapping(target = "flow", source = "flow")
    @Mapping(target = "flowDirection", source = "flowDirection")
    PipeWebSocketDTO pipeToWebSocketDTO(Pipe pipe);
}
