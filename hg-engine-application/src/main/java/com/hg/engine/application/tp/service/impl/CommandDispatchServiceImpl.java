package com.hg.engine.application.tp.service.impl;

import com.hg.engine.application.tp.dto.CommandDTO;
import com.hg.engine.domain.tp.annotation.CommandHandler;
import com.hg.engine.domain.tp.annotation.CommandDomainService;
import com.hg.engine.application.tp.service.CommandDispatchService;
import com.hg.engine.domain.tp.command.CommandParameter;
import com.hg.engine.domain.tp.command.ParameterParser;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.repository.NodeRepository;
import com.hg.engine.shared.exception.ServeException;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 基于注解的指令调度服务实现
 * 自动扫描@DomainService和@CommandHandler注解，构建指令路由表
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommandDispatchServiceImpl implements CommandDispatchService {

    private final ApplicationContext applicationContext;
    private final NodeRepository nodeRepository;
    private final ParameterParser parameterParser;
    /**
     * 指令路由表：commandType -> CommandHandlerInfo
     */
    private final Map<String, List<CommandHandlerInfo>> commandHandlers = new HashMap<>();

    /**
     * 初始化指令路由表
     */
    @PostConstruct
    public void initCommandHandlers() {
        log.info("开始扫描指令处理器...");
        // 获取所有标记了@DomainService的Bean
        Map<String, Object> domainServices = applicationContext.getBeansWithAnnotation(CommandDomainService.class);
        for (Map.Entry<String, Object> entry : domainServices.entrySet()) {
            Object serviceBean = entry.getValue();
            scanCommandHandlers(serviceBean);
        }
        log.info("指令处理器扫描完成，共注册 {} 种指令类型", commandHandlers.size());
        // 打印注册的指令处理器
        for (Map.Entry<String, List<CommandHandlerInfo>> entry : commandHandlers.entrySet()) {
            String commandType = entry.getKey();
            List<CommandHandlerInfo> handlers = entry.getValue();
            log.info("指令类型: {}", commandType);
            for (CommandHandlerInfo handler : handlers) {
                log.info("  - 处理器: {}.{}, 支持节点类型: {}, 参数类型: {}",
                        handler.getServiceClass().getSimpleName(),
                        handler.getMethod().getName(),
                        handler.getParameterType().getSimpleName());
            }
        }
    }

    /**
     * 扫描服务中的指令处理方法
     */
    private void scanCommandHandlers(Object serviceBean) {
        Class<?> serviceClass = serviceBean.getClass();
        Method[] methods = serviceClass.getDeclaredMethods();
        CommandDomainService domainAnnotation = serviceClass.getAnnotation(CommandDomainService.class);
        String nodeType = domainAnnotation.nodeType();
        for (Method method : methods) {
            CommandHandler annotation = method.getAnnotation(CommandHandler.class);
            if (annotation != null) {
                String command = annotation.command();
                Class<? extends CommandParameter> parameterType = annotation.parameterType();
                String description = annotation.description();
                CommandHandlerInfo handlerInfo = new CommandHandlerInfo(
                        serviceBean, serviceClass, method, nodeType, command, parameterType, description
                );
                commandHandlers.computeIfAbsent(nodeType, k -> new ArrayList<>()).add(handlerInfo);
                log.debug("注册指令处理器: commandType={}, method={}.{}, parameterType={}",
                        command, serviceClass.getSimpleName(), method.getName(),
                        parameterType.getSimpleName());
            }
        }
    }

    /**
     * 调度指令
     */
    private void dispatch(String nodeId, String command, Object parameters) {
        log.info("调度指令: nodeId={}, type={}", nodeId, command);
        try {
            // 1. 获取节点信息
            Node node = nodeRepository.getById(nodeId);
            if (node == null) {
                throw new ServeException("节点不存在: " + nodeId);
            }
            String nodeType = node.getType();
            // 2. 查找匹配的指令处理器
            CommandHandlerInfo handler = findHandler(command, nodeType);
            if (handler == null) {
                throw new ServeException(String.format("未找到指令处理器: type=%s, nodeType=%s",
                        command, nodeType));
            }
            // 3. 解析参数
            CommandParameter parsedParameter = parseParameter(parameters, handler.getParameterType());
            // 4. 调用指令处理方法
            invokeHandler(handler, nodeId, parsedParameter);
            log.info("指令调度成功: nodeId={}, type={}", nodeId, command);
        } catch (Exception e) {
            log.error("指令调度失败: nodeId={}, type={}", nodeId, command, e);
            throw e;
        }
    }

    /**
     * 解析参数
     */
    private CommandParameter parseParameter(Object rawParameters, Class<? extends CommandParameter> parameterType) {
        if (parameterType == CommandParameter.class) {
            // 如果没有指定具体的参数类型，返回null
            return null;
        }
        return parameterParser.parseParameter(rawParameters, parameterType);
    }

    /**
     * 查找匹配的指令处理器
     */
    private CommandHandlerInfo findHandler(String nodeType, String command) {
        List<CommandHandlerInfo> handlers = commandHandlers.get(nodeType);
        if (handlers == null || handlers.isEmpty()) {
            return null;
        }
        for (CommandHandlerInfo handler : handlers) {
            if (handler.getCommand().equals(command)) {
                return handler;
            }
        }
        return null;
    }

    /**
     * 调用指令处理方法
     */
    private void invokeHandler(CommandHandlerInfo handler, String nodeId, CommandParameter parameter) {
        try {
            Method method = handler.getMethod();
            Object service = handler.getServiceInstance();
            method.invoke(service, nodeId, parameter);
        } catch (Exception e) {
            throw new ServeException("调用指令处理方法失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有支持的指令类型
     */
    @Override
    public Map<String, List<String>> getSupportedCommands() {
        Map<String, List<String>> result = new HashMap<>();
        for (Map.Entry<String, List<CommandHandlerInfo>> entry : commandHandlers.entrySet()) {
            String nodeType = entry.getKey();
            List<String> descriptions = new ArrayList<>();
            for (CommandHandlerInfo handler : entry.getValue()) {
                String desc = String.format("节点类型：%s,指令：%s, 参数类型: %s",
                        nodeType,
                        handler.getCommand(),
                        handler.getParameterType().getName());
                descriptions.add(desc);
            }
            result.put(nodeType, descriptions);
        }

        return result;
    }

    @Transactional
    @Override
    public void dispatch(CommandDTO dto) {
        this.dispatch(dto.getNodeId(), dto.getCommand(), dto.getParameter());
    }

    /**
     * 指令处理器信息
     */
    @Data
    private static class CommandHandlerInfo {
        private final Object serviceInstance;
        private final Class<?> serviceClass;
        private final Method method;
        private final String command;
        private final String nodeType;
        private final Class<? extends CommandParameter> parameterType;
        private final String description;

        public CommandHandlerInfo(Object serviceInstance, Class<?> serviceClass, Method method,
                                  String nodeType,
                                  String command,
                                  Class<? extends CommandParameter> parameterType, String description) {
            this.serviceInstance = serviceInstance;
            this.serviceClass = serviceClass;
            this.method = method;
            this.command = command;
            this.parameterType = parameterType;
            this.description = description;
            this.nodeType = nodeType;
        }
    }
}
