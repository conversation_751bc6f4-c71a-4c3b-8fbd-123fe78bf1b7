package com.hg.engine.application.websocket.service;

import com.hg.engine.domain.websocket.model.Topic;
import com.hg.engine.domain.websocket.model.TopicSubscription;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.model.WebSocketSession;
import com.hg.engine.domain.websocket.repository.WebSocketSessionRepository;
import com.hg.engine.domain.websocket.service.TopicSubscriptionService;
import com.hg.engine.domain.websocket.service.WebSocketDomainService;
import com.hg.engine.domain.websocket.service.WebSocketMessageSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * WebSocket应用服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketApplicationService {
    
    private final WebSocketDomainService domainService;
    private final WebSocketSessionRepository sessionRepository;
    private final TopicSubscriptionService subscriptionService;
    private final WebSocketMessageSender messageSender;
    
    /**
     * 获取会话信息
     */
    public Optional<WebSocketSession> getSession(String sessionId) {
        return sessionRepository.findById(sessionId);
    }
    
    /**
     * 获取用户的所有会话
     */
    public List<WebSocketSession> getUserSessions(String userId) {
        return sessionRepository.findByUserId(userId);
    }
    
    /**
     * 获取所有活跃会话
     */
    public List<WebSocketSession> getActiveSessions() {
        return sessionRepository.findAllActive();
    }
    
    /**
     * 手动订阅主题
     */
    public void subscribeToTopic(String sessionId, String topic) {
        subscriptionService.subscribe(sessionId, topic);
        
        WebSocketMessage response = WebSocketMessage.success("订阅成功: " + topic);
        messageSender.sendToSession(sessionId, response);
        
        log.info("手动订阅主题: sessionId={}, topic={}", sessionId, topic);
    }
    
    /**
     * 手动取消订阅主题
     */
    public void unsubscribeFromTopic(String sessionId, String topic) {
        subscriptionService.unsubscribe(sessionId, topic);
        
        WebSocketMessage response = WebSocketMessage.success("取消订阅成功: " + topic);
        messageSender.sendToSession(sessionId, response);
        
        log.info("手动取消订阅主题: sessionId={}, topic={}", sessionId, topic);
    }
    
    /**
     * 获取会话的订阅列表
     */
    public List<TopicSubscription> getSessionSubscriptions(String sessionId) {
        return subscriptionService.getSubscriptions(sessionId);
    }
    
    /**
     * 获取主题的订阅者列表
     */
    public List<String> getTopicSubscribers(String topic) {
        return subscriptionService.getSubscribedSessions(topic);
    }
    
    /**
     * 发送消息到指定会话
     */
    public void sendMessageToSession(String sessionId, Topic topic, Object payload) {
        WebSocketMessage message = WebSocketMessage.of(topic, payload);
        messageSender.sendToSession(sessionId, message);

        log.debug("发送消息到会话: sessionId={}, topic={}", sessionId, topic);
    }
    
    /**
     * 发送消息到指定用户
     */
    public void sendMessageToUser(String userId, Topic topic, Object payload) {
        WebSocketMessage message = WebSocketMessage.of(topic, payload);
        messageSender.sendToUser(userId, message);

        log.debug("发送消息到用户: userId={}, topic={}", userId, topic);
    }
    
    /**
     * 发布消息到主题
     */
    public void publishToTopic(Topic topic, Object payload) {
        WebSocketMessage message = WebSocketMessage.of(topic, payload);
        messageSender.sendToTopic(topic, message);

        log.debug("发布消息到主题: topic={}", topic);
    }
    
    /**
     * 广播消息
     */
    public void broadcastMessage(Topic topic, Object payload) {
        WebSocketMessage message = WebSocketMessage.of(topic, payload);
        messageSender.broadcast(message);

        log.debug("广播消息: topic={}", topic);
    }
    
    /**
     * 发送系统通知
     */
    public void sendSystemNotification(String message) {
        WebSocketMessage notification = new WebSocketMessage(
            com.hg.engine.domain.websocket.model.Topic.NOTIFICATION,
            message
        );
        messageSender.broadcast(notification);
        log.info("发送系统通知: message={}", message);
    }
    
    /**
     * 强制断开会话
     */
    public void disconnectSession(String sessionId) {
        domainService.handleDisconnect(sessionId);
        log.info("强制断开会话: sessionId={}", sessionId);
    }
    
    /**
     * 清理超时会话
     */
    public void cleanupTimeoutSessions(long timeoutSeconds) {
        domainService.cleanupTimeoutSessions(timeoutSeconds);
    }
    
    /**
     * 获取会话统计信息
     */
    public WebSocketDomainService.SessionStats getSessionStats() {
        return domainService.getSessionStats();
    }
    
    /**
     * 获取所有主题列表
     */
    public List<String> getAllTopics() {
        return subscriptionService.getAllTopics();
    }
    
    /**
     * 获取主题订阅数量
     */
    public long getTopicSubscriptionCount(String topic) {
        return subscriptionService.getSubscriptionCount(topic);
    }
    
    /**
     * 检查会话是否在线
     */
    public boolean isSessionOnline(String sessionId) {
        return messageSender.isSessionOnline(sessionId);
    }
}
