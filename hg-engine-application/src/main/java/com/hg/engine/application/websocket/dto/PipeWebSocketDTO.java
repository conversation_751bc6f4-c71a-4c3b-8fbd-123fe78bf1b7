package com.hg.engine.application.websocket.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Pipe WebSocket传输对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PipeWebSocketDTO extends NodeWebSocketDTO {
    
    /**
     * 管道中流体是否流动
     * true: 流动
     * false: 静止
     */
    private Boolean flow;
    
    /**
     * 流动方向
     * forward: 正向流动
     * reverse: 反向流动
     */
    private String flowDirection;
}
