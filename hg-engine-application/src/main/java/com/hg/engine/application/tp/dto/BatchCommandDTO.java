package com.hg.engine.application.tp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量指令请求DTO
 */
@Data
@Schema(description = "批量指令请求")
public class BatchCommandDTO {

    /**
     * 指令列表
     */
    @NotEmpty(message = "指令列表不能为空")
    @Valid
    @Schema(description = "指令列表", required = true)
    private List<CommandItem> commands;

    /**
     * 遇到错误是否停止
     */
    @Schema(description = "遇到错误是否停止", example = "true")
    private boolean stopOnError = true;

    /**
     * 指令项
     */
    @Data
    @Schema(description = "指令项")
    public static class CommandItem {

        /**
         * 节点ID
         */
        @NotNull(message = "节点ID不能为空")
        @Schema(description = "节点ID", example = "valve-001", required = true)
        private String nodeId;

        /**
         * 指令类型
         */
        @NotNull(message = "指令类型不能为空")
        @Schema(description = "指令类型", example = "OPEN_VALVE", required = true)
        private String type;

        /**
         * 指令参数
         */
        @Schema(description = "指令参数", example = "{\"openMode\": [\"pipe-002\"]}")
        private Object parameters;
    }
}
