package com.hg.engine.application.websocket.listener;

import com.hg.engine.application.websocket.convertor.NodeWebSocketConverter;
import com.hg.engine.application.websocket.dto.NodeWebSocketDTO;
import com.hg.engine.domain.tp.event.NodeEvent;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.repository.NodeRepository;
import com.hg.engine.domain.websocket.model.Topic;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.service.WebSocketMessageSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * NodeEvent WebSocket监听器
 * 监听所有NodeEvent事件，将Node数据转换为WebSocket消息并推送给订阅的客户端
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NodeEventWebSocketListener {

    private final NodeRepository nodeRepository;

    private final NodeWebSocketConverter nodeWebSocketConverter;
    
    private final WebSocketMessageSender messageSender;

    /**
     * 监听所有NodeEvent事件
     */
    @EventListener
    public void handleNodeEvent(NodeEvent event) {
        try {
            log.debug("处理NodeEvent: eventType={}, nodeId={}",
                    event.getClass().getSimpleName(), event.getId());
            // 获取Node数据
            Node node = nodeRepository.getById(event.getId());
            if (node == null) {
                log.warn("Node不存在: nodeId={}", event.getId());
                return;
            }
            // 转换为WebSocket DTO（过滤敏感属性）
            NodeWebSocketDTO nodeDTO = nodeWebSocketConverter.nodeToWebSocketDTO(node);
            // 创建WebSocket消息
            WebSocketMessage message = WebSocketMessage.nodeEvent(event.getId(), nodeDTO);
            // 推送给订阅了NODE_EVENTS主题的客户端
            messageSender.sendToTopic(Topic.NODE_EVENTS, message);
            log.debug("NodeEvent已推送到WebSocket: eventType={}, nodeId={}",
                    event.getClass().getSimpleName(), event.getId());
        } catch (Exception e) {
            log.error("处理NodeEvent失败: eventType={}, nodeId={}",
                    event.getClass().getSimpleName(), event.getId(), e);
        }
    }
}
