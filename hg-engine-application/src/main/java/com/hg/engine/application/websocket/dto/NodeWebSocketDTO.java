package com.hg.engine.application.websocket.dto;

import lombok.Data;

/**
 * Node WebSocket传输对象
 * 不包含label、hide、lock、lastNode、nextNode、property属性
 */
@Data
public class NodeWebSocketDTO {
    
    /**
     * 组件唯一标识符
     */
    private String id;
    
    /**
     * 组件类型
     */
    private String type;
    
    /**
     * 视图ID
     */
    private String viewId;
    
    /**
     * 组件名称
     */
    private String name;
    
    /**
     * 系统ID
     */
    private String systemId;
}
