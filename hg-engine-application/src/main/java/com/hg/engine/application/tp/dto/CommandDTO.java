package com.hg.engine.application.tp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 执行指令请求DTO
 */
@Data
@Schema(description = "执行指令请求")
public class CommandDTO {

    /**
     * 节点ID
     */
    @NotBlank(message = "节点ID不能为空")
    @Schema(description = "节点ID", example = "valve-001", required = true)
    private String nodeId;

    /**
     * 指令类型
     */
    @NotBlank(message = "指令类型不能为空")
    @Schema(description = "指令类型", example = "OPEN_VALVE", required = true)
    private String command;

    /**
     * 指令参数
     */
    @Schema(description = "指令参数", example = "{\"openMode\": [\"pipe-002\", \"pipe-003\"]}")
    private Object parameter;
}
