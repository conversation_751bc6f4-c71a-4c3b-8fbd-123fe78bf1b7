package com.hg.engine.application.tp.service;

import com.hg.engine.application.tp.dto.CommandDTO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 指令调度服务接口
 */
public interface CommandDispatchService {
    

    /**
     * 获取所有支持的指令类型
     * 
     * @return 指令类型映射表
     */
    Map<String, List<String>> getSupportedCommands();

    void dispatch(@Valid CommandDTO dto);
}
