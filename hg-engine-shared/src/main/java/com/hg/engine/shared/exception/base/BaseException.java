package com.hg.engine.shared.exception.base;

import com.hg.engine.shared.common.util.MessageUtil;
import com.hg.engine.shared.common.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础异常
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseException extends RuntimeException {
    static final long serialVersionUID = 1L;

    /**
     * 错误
     */
    Integer code;

    /**
     * 消息Key I18n配置文件
     */
    String msg;


    public BaseException(Integer code, String msg,Throwable cause) {
        super(StrUtil.isNotEmpty(MessageUtil.get(msg)) ? MessageUtil.get(msg) : msg, cause);
        this.code = code;
        this.msg = msg;
    }


    public BaseException(Integer code, String msg) {
        super(StrUtil.isNotEmpty(MessageUtil.get(msg)) ? MessageUtil.get(msg) : msg);
        this.code = code;
        this.msg = msg;
    }




}

