package com.hg.engine.shared.exception;


import com.hg.engine.shared.common.constant.HttpStatus;
import com.hg.engine.shared.exception.base.BaseException;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public class ServeException extends BaseException {

    public ServeException(String msg,Throwable cause) {
        super(HttpStatus.ERROR, msg, cause);
    }


    public ServeException(String msg) {
        super(HttpStatus.ERROR, msg);
    }

    public ServeException() {
        super(HttpStatus.ERROR, "服务异常");
    }

}
