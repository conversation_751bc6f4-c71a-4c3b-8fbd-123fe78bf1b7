package com.hg.engine.infrastructure.websocket.service;

import com.hg.engine.domain.websocket.model.TopicSubscription;
import com.hg.engine.domain.websocket.model.WebSocketSession;
import com.hg.engine.domain.websocket.repository.WebSocketSessionRepository;
import com.hg.engine.domain.websocket.service.TopicSubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 主题订阅服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TopicSubscriptionServiceImpl implements TopicSubscriptionService {
    
    private final WebSocketSessionRepository sessionRepository;
    
    /**
     * 存储订阅关系：sessionId -> Set<topic>
     */
    private final Map<String, Set<String>> sessionTopics = new ConcurrentHashMap<>();
    
    /**
     * 存储订阅关系：topic -> Set<sessionId>
     */
    private final Map<String, Set<String>> topicSessions = new ConcurrentHashMap<>();
    
    @Override
    public void subscribe(String sessionId, String topic) {
        subscribe(sessionId, topic, null);
    }
    
    @Override
    public void subscribe(String sessionId, String topic, String filter) {
        // 添加到会话主题映射
        sessionTopics.computeIfAbsent(sessionId, k -> ConcurrentHashMap.newKeySet()).add(topic);
        
        // 添加到主题会话映射
        topicSessions.computeIfAbsent(topic, k -> ConcurrentHashMap.newKeySet()).add(sessionId);
        
        // 更新会话中的订阅信息
        Optional<WebSocketSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            WebSocketSession session = sessionOpt.get();
            session.subscribeTopic(topic);
            sessionRepository.save(session);
        }
        
        log.debug("会话订阅主题: sessionId={}, topic={}", sessionId, topic);
    }
    
    @Override
    public void unsubscribe(String sessionId, String topic) {
        // 从会话主题映射中移除
        Set<String> topics = sessionTopics.get(sessionId);
        if (topics != null) {
            topics.remove(topic);
            if (topics.isEmpty()) {
                sessionTopics.remove(sessionId);
            }
        }
        
        // 从主题会话映射中移除
        Set<String> sessions = topicSessions.get(topic);
        if (sessions != null) {
            sessions.remove(sessionId);
            if (sessions.isEmpty()) {
                topicSessions.remove(topic);
            }
        }
        
        // 更新会话中的订阅信息
        Optional<WebSocketSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            WebSocketSession session = sessionOpt.get();
            session.unsubscribeTopic(topic);
            sessionRepository.save(session);
        }
        
        log.debug("会话取消订阅主题: sessionId={}, topic={}", sessionId, topic);
    }
    
    @Override
    public void unsubscribeAll(String sessionId) {
        Set<String> topics = sessionTopics.remove(sessionId);
        if (topics != null) {
            // 从所有主题的会话映射中移除该会话
            for (String topic : topics) {
                Set<String> sessions = topicSessions.get(topic);
                if (sessions != null) {
                    sessions.remove(sessionId);
                    if (sessions.isEmpty()) {
                        topicSessions.remove(topic);
                    }
                }
            }
        }
        
        log.debug("会话取消所有订阅: sessionId={}", sessionId);
    }
    
    @Override
    public List<TopicSubscription> getSubscriptions(String sessionId) {
        Set<String> topics = sessionTopics.get(sessionId);
        if (topics == null) {
            return List.of();
        }
        
        return topics.stream()
                .map(topic -> new TopicSubscription(sessionId, topic))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<String> getSubscribedSessions(String topic) {
        Set<String> sessions = topicSessions.get(topic);
        return sessions != null ? List.copyOf(sessions) : List.of();
    }
    
    @Override
    public boolean isSubscribed(String sessionId, String topic) {
        Set<String> topics = sessionTopics.get(sessionId);
        return topics != null && topics.contains(topic);
    }
    
    @Override
    public long getSubscriptionCount(String topic) {
        Set<String> sessions = topicSessions.get(topic);
        return sessions != null ? sessions.size() : 0;
    }
    
    @Override
    public List<String> getAllTopics() {
        return List.copyOf(topicSessions.keySet());
    }
}
