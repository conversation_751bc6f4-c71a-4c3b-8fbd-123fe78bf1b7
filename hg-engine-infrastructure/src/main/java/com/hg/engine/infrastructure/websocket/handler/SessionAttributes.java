package com.hg.engine.infrastructure.websocket.handler;

import com.hg.engine.domain.websocket.model.WebSocketSession;
import io.netty.util.AttributeKey;

/**
 * WebSocket会话属性常量
 */
public class SessionAttributes {
    
    /**
     * 会话ID属性键
     */
    public static final AttributeKey<String> SESSION_ID = AttributeKey.valueOf("sessionId");
    
    /**
     * 会话对象属性键
     */
    public static final AttributeKey<WebSocketSession> SESSION = AttributeKey.valueOf("session");
    
    /**
     * 用户ID属性键
     */
    public static final AttributeKey<String> USER_ID = AttributeKey.valueOf("userId");
    
    private SessionAttributes() {
        // 工具类，禁止实例化
    }
}
