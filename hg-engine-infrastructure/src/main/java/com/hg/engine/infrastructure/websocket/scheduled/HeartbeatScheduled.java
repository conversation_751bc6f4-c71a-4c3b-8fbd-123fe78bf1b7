package com.hg.engine.infrastructure.websocket.scheduled;

import com.hg.engine.domain.websocket.model.Topic;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.service.WebSocketDomainService;
import com.hg.engine.domain.websocket.service.WebSocketMessageSender;
import com.hg.engine.infrastructure.websocket.config.WebSocketProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * WebSocket心跳服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HeartbeatScheduled {

    private final WebSocketMessageSender messageSender;
    private final WebSocketProperties properties;
    private final WebSocketDomainService webSocketDomainService;

    /**
     * 定时发送心跳消息
     */
    @Scheduled(fixedDelayString = "#{@webSocketProperties.heartbeat.interval}")
    public void sendHeartbeat() {
        if (!properties.isEnabled()) {
            return;
        }
        try {
            WebSocketMessage heartbeat = WebSocketMessage.heartbeat();
            messageSender.sendToTopic(Topic.HEARTBEAT, heartbeat);
            log.debug("发送心跳消息到所有订阅者");
        } catch (Exception e) {
            log.error("发送心跳消息失败", e);
        }
    }

    /**
     * 定时清理超时会话
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void cleanupTimeoutSessions() {
        if (!properties.isEnabled()) {
            return;
        }
        try {
            long timeoutSeconds = properties.getHeartbeat().getTimeout() / 1000;
            webSocketDomainService.cleanupTimeoutSessions(timeoutSeconds);
        } catch (Exception e) {
            log.error("清理超时会话失败", e);
        }
    }
}
