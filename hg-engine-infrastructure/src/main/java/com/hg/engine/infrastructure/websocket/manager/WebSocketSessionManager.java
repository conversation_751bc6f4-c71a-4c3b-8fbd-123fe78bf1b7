package com.hg.engine.infrastructure.websocket.manager;

import com.alibaba.fastjson2.JSON;
import com.hg.engine.domain.websocket.model.Topic;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.model.WebSocketSession;
import com.hg.engine.domain.websocket.repository.WebSocketSessionRepository;
import com.hg.engine.domain.websocket.service.TopicSubscriptionService;
import com.hg.engine.domain.websocket.service.WebSocketMessageSender;
import com.hg.engine.infrastructure.websocket.handler.SessionAttributes;
import io.netty.channel.Channel;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.util.concurrent.GlobalEventExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket会话管理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketSessionManager implements WebSocketMessageSender {
    
    private final WebSocketSessionRepository sessionRepository;
    private final TopicSubscriptionService subscriptionService;
    
    /**
     * 存储所有活跃的Channel
     */
    private final ChannelGroup channels = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    
    /**
     * 会话ID到Channel的映射
     */
    private final Map<String, Channel> sessionChannels = new ConcurrentHashMap<>();
    
    /**
     * 添加Channel
     */
    public void addChannel(Channel channel) {
        channels.add(channel);
        
        String sessionId = channel.attr(SessionAttributes.SESSION_ID).get();
        if (sessionId != null) {
            sessionChannels.put(sessionId, channel);
            log.debug("添加WebSocket连接: sessionId={}, channelId={}", sessionId, channel.id());
        }
    }
    
    /**
     * 移除Channel
     */
    public void removeChannel(Channel channel) {
        channels.remove(channel);
        
        String sessionId = channel.attr(SessionAttributes.SESSION_ID).get();
        if (sessionId != null) {
            sessionChannels.remove(sessionId);
            log.debug("移除WebSocket连接: sessionId={}, channelId={}", sessionId, channel.id());
        }
    }
    
    @Override
    public void sendToSession(String sessionId, WebSocketMessage message) {
        Channel channel = sessionChannels.get(sessionId);
        if (channel != null && channel.isActive()) {
            try {
                String json = JSON.toJSONString(message);
                channel.writeAndFlush(new TextWebSocketFrame(json));
                log.debug("发送消息到会话: sessionId={}, message={}", sessionId, json);
            } catch (Exception e) {
                log.error("发送消息到会话失败: sessionId={}", sessionId, e);
            }
        } else {
            log.warn("会话不在线或Channel不活跃: sessionId={}", sessionId);
        }
    }
    
    @Override
    public void sendToUser(String userId, WebSocketMessage message) {
        List<WebSocketSession> sessions = sessionRepository.findByUserId(userId);
        for (WebSocketSession session : sessions) {
            if (session.isActive()) {
                sendToSession(session.getSessionId(), message);
            }
        }
    }
    
    @Override
    public void sendToTopic(Topic topic, WebSocketMessage message) {
        List<String> subscribedSessions = subscriptionService.getSubscribedSessions(topic.getValue());
        for (String sessionId : subscribedSessions) {
            sendToSession(sessionId, message);
        }
        
        log.debug("发送消息到主题: topic={}, subscribedSessions={}", topic, subscribedSessions.size());
    }
    
    @Override
    public void sendToSessions(List<String> sessionIds, WebSocketMessage message) {
        for (String sessionId : sessionIds) {
            sendToSession(sessionId, message);
        }
    }
    
    @Override
    public void broadcast(WebSocketMessage message) {
        try {
            String json = JSON.toJSONString(message);
            TextWebSocketFrame frame = new TextWebSocketFrame(json);
            channels.writeAndFlush(frame);
            log.debug("广播消息: message={}, activeChannels={}", json, channels.size());
        } catch (Exception e) {
            log.error("广播消息失败", e);
        }
    }
    
    @Override
    public boolean isSessionOnline(String sessionId) {
        Channel channel = sessionChannels.get(sessionId);
        return channel != null && channel.isActive();
    }
    
    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return channels.size();
    }
    
    /**
     * 获取所有活跃的会话ID
     */
    public List<String> getActiveSessionIds() {
        return sessionChannels.entrySet().stream()
                .filter(entry -> entry.getValue().isActive())
                .map(Map.Entry::getKey)
                .toList();
    }
    
    /**
     * 关闭指定会话的连接
     */
    public void closeSession(String sessionId) {
        Channel channel = sessionChannels.get(sessionId);
        if (channel != null) {
            channel.close();
            log.info("关闭WebSocket连接: sessionId={}", sessionId);
        }
    }
    
    /**
     * 关闭所有连接
     */
    public void closeAllSessions() {
        channels.close();
        sessionChannels.clear();
        log.info("关闭所有WebSocket连接");
    }
}
