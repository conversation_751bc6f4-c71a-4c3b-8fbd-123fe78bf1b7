package com.hg.engine.infrastructure.persistence.po;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Node("node")
@Data
public class NodePO {

    @Id
    String id;

    String viewId;

    String type;

    String name;

    String systemId;


    @Relationship(type = "CONNECTED_TO",direction = Relationship.Direction.OUTGOING)
    private List<NodePO> nextNode = new ArrayList<>();;


    private String property;
}
