package com.hg.engine.infrastructure.websocket.handler;

import com.hg.engine.infrastructure.websocket.config.WebSocketProperties;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.codec.http.websocketx.extensions.compression.WebSocketServerCompressionHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * WebSocket通道初始化器
 */
@Component
@RequiredArgsConstructor
public class WebSocketChannelInitializer extends ChannelInitializer<SocketChannel> {
    
    private final WebSocketProperties properties;
    private final WebSocketServerHandler webSocketServerHandler;
    
    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();
        
        // HTTP编解码器
        pipeline.addLast(new HttpServerCodec());
        
        // HTTP对象聚合器
        pipeline.addLast(new HttpObjectAggregator(properties.getMessage().getMaxAggregatedSize()));
        
        // 支持大文件传输
        pipeline.addLast(new ChunkedWriteHandler());
        
        // WebSocket压缩处理器
        pipeline.addLast(new WebSocketServerCompressionHandler());
        
        // WebSocket协议处理器
        pipeline.addLast(new WebSocketServerProtocolHandler(
                properties.getPath(),
                null,
                true,
                properties.getMessage().getMaxFrameSize()
        ));
        
        // 空闲状态处理器（心跳检测）
        pipeline.addLast(new IdleStateHandler(
                properties.getHeartbeat().getTimeout() / 1000,
                0,
                0,
                TimeUnit.SECONDS
        ));
        
        // 自定义WebSocket处理器
        pipeline.addLast(webSocketServerHandler);
    }
}
