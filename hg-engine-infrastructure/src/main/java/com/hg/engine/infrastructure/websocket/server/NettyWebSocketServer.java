package com.hg.engine.infrastructure.websocket.server;

import com.hg.engine.infrastructure.websocket.config.WebSocketProperties;
import com.hg.engine.infrastructure.websocket.handler.WebSocketChannelInitializer;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * Netty WebSocket服务器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NettyWebSocketServer {
    
    private final WebSocketProperties properties;
    private final WebSocketChannelInitializer channelInitializer;
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ChannelFuture channelFuture;
    
    /**
     * 启动WebSocket服务器
     */
    @PostConstruct
    public void start() {
        if (!properties.isEnabled()) {
            log.info("WebSocket服务器已禁用");
            return;
        }
        
        try {
            log.info("启动WebSocket服务器，端口: {}, 路径: {}", properties.getPort(), properties.getPath());
            
            // 创建线程组
            bossGroup = new NioEventLoopGroup(properties.getThreadPool().getBossThreads());
            workerGroup = new NioEventLoopGroup(properties.getThreadPool().getWorkerThreads());
            
            // 创建服务器启动器
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(channelInitializer)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true);
            
            // 绑定端口并启动服务器
            channelFuture = bootstrap.bind(properties.getPort()).sync();
            
            log.info("WebSocket服务器启动成功，监听端口: {}", properties.getPort());
            
            // 在新线程中等待服务器关闭
            new Thread(() -> {
                try {
                    channelFuture.channel().closeFuture().sync();
                } catch (InterruptedException e) {
                    log.warn("WebSocket服务器等待关闭被中断", e);
                    Thread.currentThread().interrupt();
                }
            }, "websocket-server-closer").start();
            
        } catch (Exception e) {
            log.error("启动WebSocket服务器失败", e);
            shutdown();
            throw new RuntimeException("启动WebSocket服务器失败", e);
        }
    }
    
    /**
     * 关闭WebSocket服务器
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭WebSocket服务器");
        
        try {
            if (channelFuture != null) {
                channelFuture.channel().close().sync();
            }
        } catch (InterruptedException e) {
            log.warn("关闭WebSocket服务器通道被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            if (workerGroup != null) {
                workerGroup.shutdownGracefully();
            }
            if (bossGroup != null) {
                bossGroup.shutdownGracefully();
            }
        }
        
        log.info("WebSocket服务器已关闭");
    }
    
    /**
     * 检查服务器是否运行中
     */
    public boolean isRunning() {
        return channelFuture != null && channelFuture.channel().isActive();
    }
    
    /**
     * 获取服务器端口
     */
    public int getPort() {
        return properties.getPort();
    }
}
