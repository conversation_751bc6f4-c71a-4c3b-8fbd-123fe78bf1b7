package com.hg.engine.infrastructure.persistence;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hg.engine.domain.tp.model.System;
import com.hg.engine.domain.tp.repository.SystemRepository;
import com.hg.engine.infrastructure.persistence.convertor.SystemConverter;
import com.hg.engine.infrastructure.persistence.po.SystemPO;
import com.hg.engine.core.tp.infrastructure.repository.db.postgre.mapper.SystemMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @author: Likefr
 * @create: 2025-07-29
 */
@Service
@RequiredArgsConstructor
public class SystemRepositoryImpl extends ServiceImpl<SystemMapper, SystemPO> implements SystemRepository {

    private final SystemConverter systemConverter;

    @Override
    public System getById(String id) {
        SystemPO po = super.getById(id);
        return systemConverter.po2domain(po);
    }

    @Override
    public void updateById(System system) {
        SystemPO po = systemConverter.domain2po(system);
        super.updateById(po);
    }

    @Override
    public void save(System system) {
        SystemPO po = systemConverter.domain2po(system);
        super.save(po);
    }
}
