package com.hg.engine.infrastructure.websocket.handler;

import com.alibaba.fastjson2.JSON;
import com.hg.engine.domain.websocket.model.WebSocketMessage;
import com.hg.engine.domain.websocket.model.WebSocketSession;
import com.hg.engine.domain.websocket.service.WebSocketDomainService;
import com.hg.engine.infrastructure.websocket.manager.WebSocketSessionManager;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketFrame;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.UUID;

/**
 * WebSocket服务器处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ChannelHandler.Sharable
public class WebSocketServerHandler extends SimpleChannelInboundHandler<WebSocketFrame> {

    private final WebSocketDomainService webSocketDomainService;
    private final WebSocketSessionManager sessionManager;

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        log.debug("WebSocket连接建立: {}", ctx.channel().id());
        super.channelActive(ctx);
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        // WebSocket握手完成后调用
        String sessionId = generateSessionId();
        String clientIp = getClientIp(ctx);

        // 创建WebSocket会话
        WebSocketSession session = new WebSocketSession(sessionId, "anonymous");
        session.setClientIp(clientIp);

        // 将会话ID存储到Channel属性中
        ctx.channel().attr(SessionAttributes.SESSION_ID).set(sessionId);
        ctx.channel().attr(SessionAttributes.SESSION).set(session);

        // 添加到会话管理器
        sessionManager.addChannel(ctx.channel());

        // 处理连接
        webSocketDomainService.handleConnect(session);


        log.info("WebSocket握手完成: sessionId={}, clientIp={}", sessionId, clientIp);
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        String sessionId = ctx.channel().attr(SessionAttributes.SESSION_ID).get();

        // 从会话管理器移除
        sessionManager.removeChannel(ctx.channel());

        if (sessionId != null) {
            webSocketDomainService.handleDisconnect(sessionId);
            log.info("WebSocket连接断开: sessionId={}", sessionId);
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) throws Exception {
        if (frame instanceof TextWebSocketFrame) {
            handleTextFrame(ctx, (TextWebSocketFrame) frame);
        } else {
            log.warn("不支持的WebSocket帧类型: {}", frame.getClass().getSimpleName());
        }
    }

    /**
     * 处理文本帧
     */
    private void handleTextFrame(ChannelHandlerContext ctx, TextWebSocketFrame frame) {
        String sessionId = ctx.channel().attr(SessionAttributes.SESSION_ID).get();
        if (sessionId == null) {
            log.warn("会话ID为空，忽略消息");
            return;
        }

        try {
            String text = frame.text();
            log.debug("收到WebSocket消息: sessionId={}, message={}", sessionId, text);

            // 解析消息
            WebSocketMessage message = JSON.parseObject(text, WebSocketMessage.class);
            message.setSessionId(sessionId);

            // 处理消息
            webSocketDomainService.handleMessage(sessionId, message);

        } catch (Exception e) {
            log.error("处理WebSocket消息失败: sessionId={}", sessionId, e);

            // 发送错误响应
            WebSocketMessage errorMessage = WebSocketMessage.error("消息处理失败: " + e.getMessage());
            sendMessage(ctx, errorMessage);
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.READER_IDLE) {
                // 读空闲，表示客户端长时间没有发送数据
                String sessionId = ctx.channel().attr(SessionAttributes.SESSION_ID).get();
                log.warn("WebSocket连接读空闲超时，关闭连接: sessionId={}", sessionId);
                ctx.close();
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String sessionId = ctx.channel().attr(SessionAttributes.SESSION_ID).get();
        log.error("WebSocket连接异常: sessionId={}", sessionId, cause);
        ctx.close();
    }

    /**
     * 发送消息到客户端
     */
    public void sendMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            String json = JSON.toJSONString(message);
            ctx.writeAndFlush(new TextWebSocketFrame(json));
            log.debug("发送WebSocket消息: {}", json);
        } catch (Exception e) {
            log.error("发送WebSocket消息失败", e);
        }
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(ChannelHandlerContext ctx) {
        InetSocketAddress socketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        return socketAddress.getAddress().getHostAddress();
    }
}
