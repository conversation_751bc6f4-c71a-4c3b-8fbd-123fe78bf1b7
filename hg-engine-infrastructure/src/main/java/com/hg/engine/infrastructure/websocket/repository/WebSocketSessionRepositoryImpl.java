package com.hg.engine.infrastructure.websocket.repository;

import com.hg.engine.domain.websocket.model.WebSocketSession;
import com.hg.engine.domain.websocket.repository.WebSocketSessionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * WebSocket会话仓储实现（内存存储）
 */
@Slf4j
@Repository
public class WebSocketSessionRepositoryImpl implements WebSocketSessionRepository {
    
    /**
     * 内存存储会话数据
     */
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    @Override
    public void save(WebSocketSession session) {
        sessions.put(session.getSessionId(), session);
        log.debug("保存WebSocket会话: sessionId={}, userId={}", 
                 session.getSessionId(), session.getUserId());
    }
    
    @Override
    public Optional<WebSocketSession> findById(String sessionId) {
        return Optional.ofNullable(sessions.get(sessionId));
    }
    
    @Override
    public List<WebSocketSession> findByUserId(String userId) {
        return sessions.values().stream()
                .filter(session -> userId.equals(session.getUserId()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<WebSocketSession> findBySubscribedTopic(String topic) {
        return sessions.values().stream()
                .filter(session -> session.isSubscribedTo(topic))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<WebSocketSession> findAllActive() {
        return sessions.values().stream()
                .filter(WebSocketSession::isActive)
                .collect(Collectors.toList());
    }
    
    @Override
    public void delete(String sessionId) {
        WebSocketSession removed = sessions.remove(sessionId);
        if (removed != null) {
            log.debug("删除WebSocket会话: sessionId={}", sessionId);
        }
    }
    
    @Override
    public void deleteTimeoutSessions(long timeoutSeconds) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusSeconds(timeoutSeconds);
        
        List<String> timeoutSessionIds = sessions.values().stream()
                .filter(session -> session.getLastActiveTime().isBefore(cutoffTime))
                .map(WebSocketSession::getSessionId)
                .collect(Collectors.toList());
        
        for (String sessionId : timeoutSessionIds) {
            sessions.remove(sessionId);
            log.debug("删除超时会话: sessionId={}", sessionId);
        }
        
        if (!timeoutSessionIds.isEmpty()) {
            log.info("清理超时会话: count={}", timeoutSessionIds.size());
        }
    }
    
    @Override
    public long count() {
        return sessions.size();
    }
    
    @Override
    public long countActive() {
        return sessions.values().stream()
                .filter(WebSocketSession::isActive)
                .count();
    }
}
