package com.hg.engine.infrastructure.websocket.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * WebSocket配置属性
 */
@Data
@ConfigurationProperties(prefix = "netty.websocket")
@Component
public class WebSocketProperties {
    
    /**
     * 是否启用WebSocket
     */
    private boolean enabled = true;
    
    /**
     * WebSocket服务端口
     */
    private int port = 8082;
    
    /**
     * WebSocket路径
     */
    private String path = "/ws";
    
    /**
     * 心跳配置
     */
    private Heartbeat heartbeat = new Heartbeat();
    
    /**
     * 连接配置
     */
    private Connection connection = new Connection();
    
    /**
     * 消息配置
     */
    private Message message = new Message();
    
    /**
     * 默认订阅主题
     */
    private List<String> defaultTopics = List.of("system.heartbeat", "node.events");
    
    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();
    
    @Data
    public static class Heartbeat {
        /**
         * 心跳间隔(毫秒)
         */
        private long interval = 30000;
        
        /**
         * 心跳超时(毫秒)
         */
        private long timeout = 90000;
    }
    
    @Data
    public static class Connection {
        /**
         * 最大连接数
         */
        private int maxConnections = 10000;
        
        /**
         * 空闲超时(毫秒)
         */
        private long idleTimeout = 300000;
    }
    
    @Data
    public static class Message {
        /**
         * 最大帧大小(字节)
         */
        private int maxFrameSize = 65536;
        
        /**
         * 最大聚合大小(字节)
         */
        private int maxAggregatedSize = 1048576;
    }
    
    @Data
    public static class ThreadPool {
        /**
         * Boss线程数
         */
        private int bossThreads = 1;
        
        /**
         * Worker线程数
         */
        private int workerThreads = 8;
        
        /**
         * 业务线程数
         */
        private int businessThreads = 16;
    }
}
