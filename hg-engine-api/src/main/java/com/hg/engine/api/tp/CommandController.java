package com.hg.engine.api.tp;

import com.hg.engine.application.tp.dto.CommandDTO;
import com.hg.engine.application.tp.service.CommandDispatchService;
import com.hg.engine.shared.framework.web.annotation.ResultBodyConvert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 指令控制器
 * 基于注解自动路由到对应的领域服务方法
 */
@Slf4j
@RestController
@RequestMapping("/commands")
@RequiredArgsConstructor
@ResultBodyConvert
@Validated
@Tag(name = "指令管理", description = "通用指令调度接口")
public class CommandController {

    private final CommandDispatchService commandDispatchService;

    /**
     * 执行指令
     */
    @Operation(summary = "执行指令", description = "基于注解自动路由到对应的领域服务方法")
    @PostMapping("/execute")
    public void executeCommand(@Valid @RequestBody CommandDTO dto) {
        log.info("收到指令执行请求: nodeId={}, type={}",
                dto.getNodeId(), dto.getCommand());
        commandDispatchService.dispatch(dto);
    }

    /**
     * 获取支持的指令类型  返回一个文件。。。指令信息写清楚
     */
    @Operation(summary = "获取支持的指令类型", description = "获取系统支持的所有指令类型和对应的处理器")
    @GetMapping("/commands")
    public Map<String, List<String>> getSupportedCommands() {
        return commandDispatchService.getSupportedCommands();
    }

}
